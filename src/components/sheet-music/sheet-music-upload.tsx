import { Box, Button, ButtonGroup, Center, createDisclosure, HStack, Icon, Input, InputGroup, InputLeftAddon, Modal, ModalBody, ModalCloseButton, ModalContent, ModalFooter, ModalHeader, ModalOverlay, Select, SelectContent, SelectIcon, SelectListbox, SelectOption, SelectOptionIndicator, SelectOptionText, SelectPlaceholder, SelectTrigger, SelectValue, Textarea, VStack } from "@hope-ui/solid";
import { cloneDeep, isEmpty, isEqual } from "lodash-es";
import { FaSolidFileArrowUp, FaSolidX } from "solid-icons/fa";
import { Accessor, Component, createEffect, createSignal, For, onCleanup, onMount } from "solid-js";
import { createStore } from "solid-js/store";
import { useService } from "solid-services";
import { z } from "zod";
import { SheetMusicRequest, SheetMusicCategory, SheetMusicDifficultyLevel, SheetMusicConst, SheetMusicGenreTags } from "~/models/sheet-music-dbo.models";
import { AppStateActions, AppStateActions_Action } from "~/proto/pianorhythm-actions";
import { AppVPSequencerFileLoad, AppVPSequencerTrack } from "~/proto/pianorhythm-app-renditions";
import AppService from "~/services/app.service";
import DisplaysService, { Displays } from "~/services/displays.service";
import { SheetMusicService } from "~/services/sheet-music.service";
import SoundEffectsService from "~/services/sound-effects.service";
import { ab2str } from "~/util/helpers";
import SwalPR from "~/util/sweetalert";
import VPSheetSequencerControlsUI from "../midi-player/midi-vp-sequencer-ui";
import { SheetMusicContentUpload } from "./sheet-music-common";
import MotionFadeIn from "../motion/motion.fade-in";
import abcjs from "abcjs";
import "abcjs/abcjs-audio.css";
import { COMMON } from "~/util/const.common";
import NotificationService from "~/services/notification.service";

const DEFAULT_DATA_UPLOAD: SheetMusicRequest = {
  title: "",
  songArtist: undefined,
  songAlbum: undefined,
  tags: [],
  description: undefined,
  category: SheetMusicCategory.VirtualPiano,
  privacy: "Public",
  difficultyLevel: SheetMusicDifficultyLevel.Unknown,
  data: undefined,
  tempo: 120,
  totalTime: undefined,
};

type SelectListItem = {
  value: string;
  label?: string;
};

const CreateSelectList: Component<{
  defaultValue?: string | string[],
  valueSignalChange?: Accessor<string | string[] | undefined>,
  placeholder: string,
  isMultiple?: boolean,
  invalid?: boolean,
  data: SelectListItem[];
  onSetValue?: (values: string[] | string) => void;
}> = (props) => {
  const [value, setValue] = createSignal(props.defaultValue || (props.isMultiple ? [] : ""));

  const onSetValue = (value: string | string[]) => {
    setValue(value);
    props.onSetValue?.(value);
  };

  createEffect(() => {
    let change = props.valueSignalChange?.();
    if (change) setValue(change);
  });

  return (<>
    <Select
      defaultValue={props.defaultValue}
      multiple={props.isMultiple}
      value={value()}
      onChange={onSetValue}
      invalid={props.invalid}
    >
      <SelectTrigger>
        <SelectPlaceholder>{props.placeholder}</SelectPlaceholder>
        <HStack spacing={"$2"}>
          <SelectValue >{item =>
            <For each={item.selectedOptions}>
              {option =>
                <HStack spacing={"$0_5"}
                  background={"$primary1"}
                  padding={"$0_5 $1"}
                  borderRadius={"$sm"}
                >
                  <Box
                    __tooltip_title={
                      props.data.find(x => x.value == option.value)?.label
                    }
                  >{option.textValue}</Box>
                  {props.isMultiple &&
                    <Box
                      onClick={() => {
                        if (!value() || typeof value() == "string") return;
                        //@ts-ignore
                        onSetValue(value().filter(x => x != option.value));
                      }}
                      _hover={{ "background": "$primary5" }}
                    >
                      <FaSolidX
                        width={"100%"}
                        height={"100%"}
                        font-size="0.7em"
                        style={{ "margin-top": "2px" }}
                      />
                    </Box>
                  }
                </HStack>}
            </For>
          }</SelectValue>
        </HStack>
      </SelectTrigger>
      <SelectContent>
        <SelectListbox>
          <For each={props.data}>
            {item => (
              <SelectOption value={item.value}>
                <SelectOptionText>{item.value}</SelectOptionText>
                <SelectOptionIndicator />
              </SelectOption>
            )}
          </For>
        </SelectListbox>
      </SelectContent>
    </Select>
  </>);
};

const sheetMusicDataSchema = z.object({
  title: z.string().min(1, { message: "Title is required" }),
  data: z.string().min(1, { message: "Please select a file" }),
  songArtist: z.string().max(1000).optional().nullable(),
  songAlbum: z.string().max(1000).optional().nullable(),
  tags: z.array(z.string()).max(10).optional().default([]),
  description: z.string().max(1000).optional().nullable(),
  category: z.nativeEnum(SheetMusicCategory).optional().default(DEFAULT_DATA_UPLOAD.category as SheetMusicCategory),
  difficultyLevel: z.nativeEnum(SheetMusicDifficultyLevel).optional().default(DEFAULT_DATA_UPLOAD.difficultyLevel as SheetMusicDifficultyLevel),
  privacy: z.enum(["Public", "Unlisted", "Private"]).optional().default(DEFAULT_DATA_UPLOAD.privacy ?? "Public"),
  tempo: z.number().min(1).max(1000).optional().default(DEFAULT_DATA_UPLOAD.tempo as number),
});

const SheetMusicUpload: Component = () => {
  const DISPLAY_KEY = "SHEET_MUSIC_UPLOAD_MODAL";
  const { isOpen, onOpen, onClose } = createDisclosure();
  const appService = useService(AppService);
  const displayService = useService(DisplaysService);
  const sheetMusicService = useService(SheetMusicService);
  const sfxService = useService(SoundEffectsService);
  const [invalidFields, setInvalidFields] = createSignal<string[]>([]);
  const [lastModal, setLastModal] = createSignal<keyof Displays>();
  const [currentEditData, setCurrentEditData] = createSignal<SheetMusicRequest>();
  const [fields, setFields] = createStore<SheetMusicRequest>({ ...DEFAULT_DATA_UPLOAD });
  let abcFormContentElement!: HTMLDivElement;

  onMount(() => {
    let activeData = sheetMusicService().activeViewerData();

    if (sheetMusicService().editMode() && activeData) {
      let recentModal = displayService().getLastModalsOpened().indexOf("SHEET_MUSIC_DETAILS_MODAL");
      // Return to repo if we were last in the repo
      if (recentModal > -1) setLastModal("SHEET_MUSIC_REPO_MODAL");

      // let keys = Object.keys(sheetMusicData.fields).filter(x => x != "data") as (keyof SheetMusicRequest)[];
      let keys = Object.keys(fields) as (keyof SheetMusicRequest)[];
      setFields("data", ab2str(activeData.file.data as any));

      keys.forEach(key => {
        let source = activeData?.data;

        if (source) {
          let sourceKey: string = key;
          if (key == "privacy") sourceKey = "privacyStatus";
          if (key == "tempo") sourceKey = "bpm";
          setFields(key, (source as any)[sourceKey]);
        }

        setCurrentEditData(cloneDeep({
          ...DEFAULT_DATA_UPLOAD,
          title: source?.title || DEFAULT_DATA_UPLOAD.title,
          songArtist: source?.songArtist || undefined,
          songAlbum: source?.songAlbum || undefined,
          tags: source?.tags || [],
          description: source?.description || undefined,
          category: source?.category || DEFAULT_DATA_UPLOAD.category,
          privacy: (source?.privacyStatus || DEFAULT_DATA_UPLOAD.privacy) as "Public" | "Unlisted" | "Private",
          difficultyLevel: source?.difficultyLevel || DEFAULT_DATA_UPLOAD.difficultyLevel,
          tempo: source?.bpm || DEFAULT_DATA_UPLOAD.tempo,
        }));
      });

    } else {
      let uploadData = sheetMusicService().newSheetFromViewerUploadData();
      if (uploadData) { setFields("data", uploadData.data); }
    }
  });

  onCleanup(() => {
    SwalPR(sfxService).close();
    sheetMusicService().setNewSheetFromViewerUploadData({ data: undefined });
    appService().coreService()?.send_app_action(AppStateActions.create({
      action: AppStateActions_Action.VPSequencerStop,
    }), true);

    if (lastModal()) displayService().setDisplay(lastModal()!, true);
  });

  function closeModal() {
    displayService().setDisplay(DISPLAY_KEY, false);
    sheetMusicService().setEditMode(false);
    sheetMusicService().setActiveViewerData(undefined);
  }

  createEffect(async () => {
    if (displayService().getDisplay(DISPLAY_KEY)) {
      onOpen();
    } else { onClose(); }
  });

  createEffect(async () => {
    let result = await sheetMusicDataSchema.safeParseAsync(fields);
    if (!result.success) {
      let issues = result.error.issues.flatMap(x => x.path).map(x => x.toString());
      setInvalidFields(issues);
    } else {
      setInvalidFields([]);
    }
  });

  const isVpCategory = () => fields.category == SheetMusicCategory.VirtualPiano;
  const isAbcCategory = () => fields.category == SheetMusicCategory.ABCMusicNotation;

  const AbcNotationDetector = () => {
    type MetaText = {
      title?: string;
      artist?: string;
      album?: string;
      bpm?: number;
    };
    const [metaText, setMetaText] = createSignal<MetaText>();
    const lastCategory = String(fields.category);

    createEffect(() => {
      try {
        // Try to auto-select the category based on the file data
        let rendered = abcjs.renderAbc(abcFormContentElement, fields.data ?? "", {
          showDebug: COMMON.IS_DEV_MODE ? ["grid"] : undefined,
        });

        if (!rendered || !rendered?.[0] || !rendered?.[0].lines?.length) {
          throw new Error("Invalid ABC Notation");
        }

        let notionObject = rendered[0];
        let meta = notionObject?.metaText;
        if (isEmpty(meta)) throw new Error("Invalid ABC Notation");

        setMetaText({
          title: meta?.title,
          artist: meta?.composer,
          album: meta?.source,
          bpm: notionObject.getBpm(),
        });
      } catch {
        setFields("category", (lastCategory ?? SheetMusicCategory.VirtualPiano) as any);
        setMetaText(undefined);
      }
    });

    return (<>
      <Center w="100%" mb="1rem">
        <Box display="none" ref={abcFormContentElement}></Box>
        {metaText() &&
          <Button
            fontSize="$sm"
            as="pre"
            background={"$primaryDark1"}
            textAlign={"center"}
            onMouseDown={() => {
              if (metaText()?.title) setFields("title", metaText()?.title!);
              if (metaText()?.artist) setFields("songArtist", metaText()?.artist!);
              if (metaText()?.album) setFields("songAlbum", metaText()?.album!);
              if (metaText()?.bpm) setFields("tempo", metaText()?.bpm!);
              setFields("category", SheetMusicCategory.ABCMusicNotation);
            }}
          >ABC Notation detected. <br />Click to autofill
          </Button>
        }
      </Center>
    </>);
  };

  return (<>
    <Modal
      centered opened={isOpen()} onClose={closeModal}
      scrollBehavior={"inside"}
      closeOnOverlayClick={false}
      size="full"
    >
      <ModalOverlay zIndex={"calc(var(--hope-zIndices-overlay) + 100)"} />
      <ModalContent>
        <ModalCloseButton />
        <ModalHeader><Icon as={FaSolidFileArrowUp} /> Sheet Music - {sheetMusicService().editMode() ? "Edit" : "New Upload"}</ModalHeader>

        <ModalBody padding={30} >
          {!isAbcCategory() && <AbcNotationDetector />}
          <Box>
            {(fields.data && isVpCategory()) &&
              <MotionFadeIn>
                <Box
                  borderTopRadius={5}
                  background={"$primaryDark1"} paddingTop={10}>
                  <VPSheetSequencerControlsUI
                    input={AppVPSequencerFileLoad.create({
                      data: cloneDeep(fields.data),
                      fileName: cloneDeep(fields.title) || "Untitled",
                      tracks: [
                        AppVPSequencerTrack.create({
                          index: 0,
                          tempo: fields.tempo || 120,
                        })
                      ]
                    })}
                  />
                </Box>
              </MotionFadeIn>
            }

            <SheetMusicContentUpload
              allowCreateNew
              categoryType={fields.category as SheetMusicCategory}
              isVPSheet={fields.category == SheetMusicCategory.VirtualPiano}
              displayDataAfterUpload
              value={fields.data}
              onNewBodyContentUpdate={(text) => { setFields("data", text); }}
              onLoad={(data) => { setFields("data", atob(data.data as any)); }}
            />
          </Box>

          <VStack spacing="$4" marginTop={50}>
            {/* Title */}
            <InputGroup>
              <InputLeftAddon>Title</InputLeftAddon>
              <Input
                value={fields.title}
                invalid={invalidFields().includes("title")}
                onInput={(e) => setFields("title", (e.target as any).value || null)}
                placeholder="title" maxlength={SheetMusicConst.MaxSongName} />
            </InputGroup>

            {/* Artist & Album */}
            <HStack spacing="$7" w="100%">
              <InputGroup>
                <InputLeftAddon>Artist</InputLeftAddon>
                <Input
                  value={fields.songArtist}
                  invalid={invalidFields().includes("songArtist")}
                  onInput={(e) => setFields("songArtist", (e.target as any).value || null)}
                  placeholder="original artist" maxlength={SheetMusicConst.MaxArtistNameLength} />
              </InputGroup>
              <InputGroup>
                <InputLeftAddon>Album</InputLeftAddon>
                <Input
                  value={fields.songAlbum}
                  invalid={invalidFields().includes("songAlbum")}
                  onInput={(e) => setFields("songAlbum", (e.target as any).value || null)}
                  placeholder="original artist's album" maxlength={SheetMusicConst.MaxArtistAlbumLength} />
              </InputGroup>
            </HStack>

            {/* Description */}
            <InputGroup>
              <InputLeftAddon>Description</InputLeftAddon>
              <Textarea
                value={fields.description}
                invalid={invalidFields().includes("description")}
                onInput={(e) => setFields("description", (e.target as any).value || null)}
                minH="5rem" maxH="15rem" resize={"vertical"}
                maxlength={SheetMusicConst.MaxDescriptionLength} />
            </InputGroup>

            {/* Tempo */}
            <InputGroup>
              <InputLeftAddon>Tempo</InputLeftAddon>
              <Input
                type="number"
                value={fields.tempo}
                invalid={invalidFields().includes("tempo")}
                onInput={(e) => setFields("tempo", parseInt((e.target as any).value) || undefined)}
                placeholder="tempo/bpm"
                min={1}
                max={1000}
              />
            </InputGroup>

            {/* Tags */}
            <InputGroup>
              <InputLeftAddon>Tags</InputLeftAddon>
              <CreateSelectList
                data={SheetMusicGenreTags.map(tag => ({ value: tag, label: tag }))}
                isMultiple
                defaultValue={fields.tags}
                invalid={invalidFields().includes("tags")}
                onSetValue={(val) => setFields("tags", val)}
                placeholder="Choose some tags..." />
            </InputGroup>

            {/* Category */}
            <InputGroup>
              <InputLeftAddon>Category</InputLeftAddon>
              <CreateSelectList
                defaultValue={String(fields.category)}
                valueSignalChange={() => fields.category}
                invalid={invalidFields().includes("category")}
                onSetValue={(val) => setFields("category", val as string)}
                data={[
                  { value: SheetMusicCategory.MultiplayerPiano, label: SheetMusicCategory.MultiplayerPiano },
                  { value: SheetMusicCategory.VirtualPiano, label: SheetMusicCategory.VirtualPiano },
                  { value: SheetMusicCategory.ABCMusicNotation, label: "A text-based music notation system and the de facto standard for folk and traditional music." },
                ]}
                placeholder="Choose a category..." />
            </InputGroup>

            {/* Difficulty */}
            <InputGroup>
              <InputLeftAddon>Difficulty</InputLeftAddon>
              <CreateSelectList
                defaultValue={fields.difficultyLevel}
                invalid={invalidFields().includes("difficultyLevel")}
                onSetValue={(val) => setFields("difficultyLevel", val as string)}
                data={[
                  { value: SheetMusicDifficultyLevel.Beginner },
                  { value: SheetMusicDifficultyLevel.BeginnerPlus },
                  { value: SheetMusicDifficultyLevel.Intermediate },
                  { value: SheetMusicDifficultyLevel.IntermediatePlus },
                  { value: SheetMusicDifficultyLevel.Advanced },
                  { value: SheetMusicDifficultyLevel.AdvancedPlus },
                  { value: SheetMusicDifficultyLevel.Unknown },
                ]}
                placeholder="Choose an appropriate difficulty..." />
            </InputGroup>

            {/* Privacy */}
            <InputGroup>
              <InputLeftAddon>Privacy Level</InputLeftAddon>
              <CreateSelectList
                defaultValue={fields.privacy}
                data={["Public", "Private", "Unlisted"].map(item => ({ value: item }))}
                placeholder="Choose a privacy level" />
            </InputGroup>
          </VStack>
        </ModalBody>

        <ModalFooter h="3rem" padding={0}>
          <ButtonGroup size={"sm"} spacing="$2" padding={"$2"}>
            <Button variant={"outline"} onClick={closeModal}>Cancel</Button>

            {/* Submit */}
            <Button
              onClick={async () => {
                let message = "Your changes will have to be reapproved after submitting.";
                if (appService().isClientMod()) message = "Your changes will be submitted once you click OK!";
                if (!sheetMusicService().editMode() && fields.privacy == "Public") message = "Your upload will have to be approved before it shows up on the public listing.";

                SwalPR(sfxService).fire({
                  icon: "info",
                  html: message,
                  showCancelButton: true,
                  confirmButtonText: "Okay!",
                  showCloseButton: true,
                  allowEscapeKey: true,
                  allowEnterKey: true,
                  showLoaderOnConfirm: true,
                  allowOutsideClick: () => !SwalPR().isLoading(),
                  preConfirm: async () => {
                    let input = await sheetMusicDataSchema.safeParseAsync(fields);
                    if (!input.success) { throw new Error("Invalid input"); }
                    let data = input.data as SheetMusicRequest;
                    let id = sheetMusicService().activeViewerData()?.data.id;
                    return (sheetMusicService().editMode() ? sheetMusicService().updateSheetMusic(data, id) : sheetMusicService().uploadSheetMusic(data));
                  }
                }).then((result) => {
                  if (result.isConfirmed) {
                    NotificationService.show({
                      title: "Sheet Music Upload",
                      description: "Sheet music upload success!",
                      type: "success",
                    });
                    displayService().setDisplay("SHEET_MUSIC_UPLOAD_MODAL", false);
                  }
                }).catch((err) => {
                  console.error(err);
                  let errorMessage = `Error: <b>${err.message}</b>`;

                  try {
                    let validation_errors = JSON.parse(err.message) as string[][];
                    errorMessage = ``;
                    validation_errors.forEach((error) => {
                      let key = error[0];
                      let desc = error[1];
                      errorMessage += `<br>[<b>${key}</b>]: ${desc}`;
                    });
                  } catch { }

                  SwalPR(sfxService).fire({
                    icon: "error",
                    html: `
                          Oof, sorry. Failed to upload sheet music!
                          <br><br>
                          ${errorMessage}
                          <br><br>
                          Please try again.
                        `
                  });
                });
              }}
              disabled={
                invalidFields().length > 0
                || isEqual(currentEditData(), cloneDeep(fields))
              }
              background={"$primaryDark1"}
            >
              Submit
            </Button>
          </ButtonGroup>
        </ModalFooter>
      </ModalContent>
    </Modal>
  </>);
};

export default SheetMusicUpload;