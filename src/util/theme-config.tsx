import { Box, HopeThemeConfig } from "@hope-ui/solid";

const ThemeConfig: HopeThemeConfig = {
  initialColorMode: "dark",
  darkTheme: {
    colors: {
      tooltipContent: "#2b2d34",
      tooltipArrow: "#2b2d34",
      primaryDark1: "#1f2126",
      primaryDark2: "#2b2d34",
      primaryDarkAlpha: "#2b2d34B2",
      primaryDarkAlpha2: "#2b2d34DE",
      primaryAlpha: "#363942F2",
      primaryAlpha2: "#363942c2",
      primaryAlpha3: "#3639425c",
      primaryLight: "#4d515e",
      primaryGradient1: "linear-gradient(130deg, var(--hope-colors-primaryLight), var(--hope-colors-primaryDark1))",
      primary1: "#363942",
      primary2: "#434650",
      primary3: "#51545e",
      primary4: "#5f626c",
      primary5: "#6e717b",
      primary6: "#7d808a",
      primary7: "#8c8f9a",
      primary8: "#9b9fa9",
      primary9: "#bbbfca",
      primary10: "#cccfda",
      primary11: "#dce0eb",
      primary12: "#edf0fc",
      accent1: "#00d1b2",
      accent2: "#15d6b6",
      accent3: "#22dabb",
      accent4: "#2bdfbf",
      accent5: "#33e3c3",
      accent6: "#3be8c8",
      accent7: "#42eccc",
      accent8: "#48f1d0",
      accent9: "#4ef6d5",
      accent10: "#54fad9",
      accent11: "#5affde",
      accent12: "#60ffe2",
      tertiary1: "#ef9e08",
      tertiary5: "#ffbe54",
      primaryGradient: "linear-gradient(115deg, $primaryLight, $primaryDark1)"
    },
  },
  components: {
    Input: {
      baseStyle: {
        input: {
          backgroundColor: "$primary1",
          border: "1px solid var(--hope-colors-neutral9)",
          _hover: {
            border: "1px solid var(--hope-colors-neutral11)",
          },
          _focus: {
            boxShadow: "none",
          },
        }
      }
    },
    Radio: {
      baseStyle: {
        label: {
          color: "$neutral12",
          _hover: {
            color: "$neutral1",
          }
        }
      }
    },
    Select: {
      baseStyle: {
        content: {
          backgroundColor: "$primaryDark1",
        },
        singleValue: {
          backgroundColor: "$primaryDark1",
        },
        trigger: {
          backgroundColor: "$primaryDark1",
          _focus: {
            backgroundColor: "$primaryDark1",
          },
        },
        option: {
          _hover: {
            backgroundColor: "$accent2",
          }
        }
      }
    },
    Switch: {
      baseStyle: {
        control: {
          backgroundColor: "var(--hope-colors-neutral10)"
        }
      }
    },
    Tooltip: {
      baseStyle: {
        backgroundColor: "$primaryDark2",
        color: "white"
      }
    },
    Notification: {
      baseStyle: {
        root: {
          backgroundColor: "$primaryAlpha",
          boxShadow: "none",
          border: "2px solid $neutral12",
          maxWidth: "$xs",
        },
        title: {
          fontWeight: "$bold",
        },
        description: {
          color: "$neutral11"
        }
      }
    },
    Kbd: {
      baseStyle: {
        padding: "5px",
        color: "$neutral12",
        backgroundColor: "$primary3",
        borderColor: "$neutral12",
        transition: "background-color 0.2s ease, transform 0.1s ease-in-out"
      }
    },
    Button: {
      baseStyle: {
        root: {
          backgroundColor: "$primary1",
          color: "$neutral12",
          transition: "color 0.1s ease, background-color 0.1s ease",
          _focus: {
            boxShadow: "none",
            // color: "$neutral1",
          },
          _active: {
            transform: "scale(1.025)",
            // color: "$neutral1",
          },
          _hover: {
            color: "$neutral1",
            backgroundColor: "$accent2",
          },
          _disabled: {
            color: "inherit !important",
            opacity: 0.4,
            pointerEvents: "none"
          }
        }
      }
    },
    Modal: {
      baseStyle: {
        header: {
          background: "$neutral12",
          color: "$primary1",
          borderTopRadius: 5,
          paddingTop: "$3",
          paddingBottom: "$3",
        },
        body: {
          backgroundColor: "$primary1",
        },
        footer: {
          background: "$primary1",
          borderTop: "solid 1px gray",
          borderBottomRadius: 5,
          paddingBottom: "$2"
        },
        closeButton: {
          outline: "none",
          color: "$primary1",
          top: "$2",
          _focus: {
            boxShadow: "none",
          },
          _hover: {
            color: "$neutral1",
            "backgroundColor": "$primary1"
          }
        }
      }
    },
    CloseButton: {
      defaultProps: {
        icon: <Box _hover={{"color": "white"}}>✖</Box>
      },
      baseStyle: {
        backgroundColor: "$primary1",
        _hover: {
          backgroundColor: "$accent1",
        }
      }
    },
  }
};

export default ThemeConfig;